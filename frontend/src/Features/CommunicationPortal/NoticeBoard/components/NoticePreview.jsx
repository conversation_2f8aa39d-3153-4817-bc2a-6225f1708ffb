import React, { useState } from "react";
import { Flag, Image as ImageIcon } from "lucide-react";
import { VscFilePdf } from "react-icons/vsc";
import { FaFileWord } from "react-icons/fa";
import { HiUserCircle } from "react-icons/hi";
import { FaUserGroup } from "react-icons/fa6";
import { IoIosNotificationsOutline } from "react-icons/io";
import { useUserCount } from "../hooks/useUserCount";

/**
 * NoticePreview Component
 * Real-time preview of the notice as it will appear when posted
 */
const NoticePreview = ({ data, currentUser, isInModal = false }) => {
  const [expandedTitle, setExpandedTitle] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // Use the custom hook for user count calculation
  // Handle both selectedUnits (from create/edit form) and target_units_data (from history/existing notices)
  const targetUnits =
    data.selectedUnits || data.target_units_data?.map((unit) => unit.id) || [];
  const { userCount, loading: loadingUserCount } = useUserCount(targetUnits);

  // Get notice status
  const getStatus = () => {
    if (!data.startDate || !data.startTime || !data.endDate || !data.endTime) {
      return { status: "Draft", color: "#6B7280", bgColor: "#F9FAFB" };
    }

    try {
      const now = new Date();

      // Handle different date formats and ensure proper parsing
      const parseDateTime = (dateStr, timeStr) => {
        if (!dateStr || !timeStr) return null;

        let formattedDate;
        if (typeof dateStr === 'string' && dateStr.includes('-')) {
          // Handle DD-MM-YYYY format
          const [day, month, year] = dateStr.split('-');
          formattedDate = `${year}-${month}-${day}`;
        } else {
          formattedDate = dateStr;
        }

        return new Date(`${formattedDate}T${timeStr}`);
      };

      const startDateTime = parseDateTime(data.startDate, data.startTime);
      const endDateTime = parseDateTime(data.endDate, data.endTime);

      if (!startDateTime || !endDateTime) {
        return { status: "Draft", color: "#6B7280", bgColor: "#F9FAFB" };
      }

      if (now < startDateTime) {
        return { status: "Upcoming", color: "#3B82F6", bgColor: "#EBF8FF" };
      } else if (now >= startDateTime && now <= endDateTime) {
        return { status: "Ongoing", color: "#10B981", bgColor: "#F0FDF4" };
      } else {
        return { status: "Expired", color: "#EF4444", bgColor: "#FEF2F2" };
      }
    } catch (error) {
      console.error("Error parsing date/time:", error);
      return { status: "Draft", color: "#6B7280", bgColor: "#F9FAFB" };
    }
  };

  // Get priority configuration
  const getPriorityConfig = (priority) => {
    const priorityMap = {
      high: { color: "#EF4444", bgColor: "#FEF2F2", label: "High" },
      medium: { color: "#F59E0B", bgColor: "#FFFBEB", label: "Medium" },
      normal: { color: "#10B981", bgColor: "#F0FDF4", label: "Normal" },
      low: { color: "#6B7280", bgColor: "#F9FAFB", label: "Low" }
    };
    return priorityMap[priority?.toLowerCase()] || priorityMap.normal;
  };

  // Format date and time
  const formatDateTime = (date, time) => {
    if (!date || !time) return "";

    let formattedDate = "";

    // Handle different date formats
    if (typeof date === "string") {
      // If date is in YYYY-MM-DD format (from Calendar component)
      if (date.includes("-") && date.length === 10) {
        const [year, month, day] = date.split("-");
        formattedDate = `${day}-${month}-${year}`;
      } else {
        // If date is already formatted or other string format
        const dateObj = new Date(date);
        const day = dateObj.getDate().toString().padStart(2, "0");
        const month = (dateObj.getMonth() + 1).toString().padStart(2, "0");
        const year = dateObj.getFullYear();
        formattedDate = `${day}-${month}-${year}`;
      }
    } else {
      // If date is a Date object
      const dateObj = new Date(date);
      const day = dateObj.getDate().toString().padStart(2, "0");
      const month = (dateObj.getMonth() + 1).toString().padStart(2, "0");
      const year = dateObj.getFullYear();
      formattedDate = `${day}-${month}-${year}`;
    }

    // Format time
    let formattedTime = "";
    if (time) {
      // Handle time in HH:MM format
      const [hours, minutes] = time.split(":");
      const hour24 = parseInt(hours, 10);
      const hour12 = hour24 === 0 ? 12 : hour24 > 12 ? hour24 - 12 : hour24;
      const ampm = hour24 >= 12 ? "pm" : "am";
      formattedTime = `${hour12}:${minutes}${ampm}`;
    }

    return formattedTime ? `${formattedDate} at ${formattedTime}` : formattedDate;
  };

  // Handle title expansion
  const handleTitleHover = () => {
    if (data.title && data.title.length > 25) {
      setExpandedTitle(true);
    }
  };

  const handleTitleLeave = () => {
    setExpandedTitle(false);
  };

  // Handle title click for mobile/touch devices
  const handleTitleClick = () => {
    if (data.title && data.title.length > 25) {
      setExpandedTitle(!expandedTitle);
    }
  };

  // Helper function to check if file is a PDF
  const isPDF = (fileName, fileType) => {
    if (fileType) {
      return fileType === "application/pdf";
    }
    return fileName?.toLowerCase().endsWith(".pdf");
  };

  // Helper function to check if file is a DOC
  const isDoc = (fileName, fileType) => {
    if (fileType) {
      return (
        fileType === "application/msword" ||
        fileType ===
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
      );
    }
    return fileName?.toLowerCase().match(/\.(doc|docx)$/);
  };

  // Image navigation functions
  const nextImage = () => {
    if (data.attachments && data.attachments.length > 1) {
      setCurrentImageIndex((prev) =>
        prev === data.attachments.length - 1 ? 0 : prev + 1
      );
    }
  };

  const prevImage = () => {
    if (data.attachments && data.attachments.length > 1) {
      setCurrentImageIndex((prev) =>
        prev === 0 ? data.attachments.length - 1 : prev - 1
      );
    }
  };

  const priorityConfig = getPriorityConfig(data.priority);
  const statusConfig = getStatus();

  return (
    <div className="space-y-4 ">
      {/* Preview Header */}

      {/* Notice Card Preview */}
      <div className="border-[1px] border-primary rounded-lg overflow-hidden bg-white shadow-sm ">
        {/* Header with Author Info and Status */}
        <div className="p-4 border-b border-gray-100">
          <div className="flex items-start justify-between">
            {/* Left side - Author Info */}
            <div className="flex items-center space-x-3">
              {/* Author Avatar and Info */}
              <div className="w-8 h-8 rounded-full flex items-center justify-center">
                {data.postAs === "Creator" || data.postAs === "creator" ? (
                  <HiUserCircle className="w-8 h-8" color="gray" />
                ) : data.postAs === "Group" || data.postAs === "group" ? (
                  <FaUserGroup className="w-8 h-8" color="gray" />
                ) : data.postAs === "Member" || data.postAs === "member" ? (
                  <FaUserGroup className="w-8 h-8" color="gray" />
                ) : (
                  <HiUserCircle className="w-8 h-8" color="gray" />
                )}
              </div>
              <div>
                <h3 className="text-sm font-bold text-gray-900">
                  {data.postAs === "Group"
                    ? data.selectedGroupName || "Group Name"
                    : data.postAs === "Member"
                    ? data.selectedMemberName || "Member Name"
                    : data.authorName || "Author Name"}
                </h3>
                <p className="text-xs font-bold text-gray-900">
                  Creator {data.creatorName ||
                    data.authorName ||
                    currentUser?.fullName ||
                    currentUser?.full_name ||
                    "Not specified"}
                </p>
              </div>
            </div>

            {/* Right side - Priority Flag, Status and Notification */}
            <div className="flex items-center space-x-2">
              {/* Priority Flag */}
              {data.priority && priorityConfig && (
                <Flag
                  className="w-4 h-4"
                  style={{ color: priorityConfig.color }}
                  fill={priorityConfig.color}
                />
              )}
              <IoIosNotificationsOutline
                className="w-4 h-4"
                style={{ color: statusConfig.color }}
              />
              <span className="text-xs font-medium text-gray-700 flex items-center">
                {loadingUserCount && userCount === 0 ? (
                  <div className="flex items-center">
                    <div className="w-3 h-3 border border-gray-400 border-t-transparent rounded-full animate-spin mr-1"></div>
                    0
                  </div>
                ) : (
                  userCount
                )}
              </span>
            </div>

            {/* Date Info */}
            <div className="mt-2 text-[10px] flex items-center gap-3">
              <span className="text-primary font-bold whitespace-nowrap">
                Start: {formatDateTime(data.startDate, data.startTime) || ""}
              </span>
              <span className="text-[#FF8682] font-bold whitespace-nowrap">
                Expire: {formatDateTime(data.endDate, data.endTime) || ""}
              </span>
            </div>

            {/* Label Info - Next line */}
            {data.label && (
              <div className="mt-1 text-xs">
                <div className="flex flex-wrap gap-1">
                  {data.label.split(",").map((label, index) => (
                    <span
                      key={index}
                      className="bg-[#F5F5F5] text-[#090909] text-[10px] px-2 py-1 rounded font-bold"
                    >
                      {label.trim()}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Content */}
          <div className="p-4">
            {/* Title */}
            <div
              className="mb-3 cursor-pointer"
              onMouseEnter={handleTitleHover}
              onMouseLeave={handleTitleLeave}
              onClick={handleTitleClick}
            >
              <h4
                className="text-sm font-bold text-gray-900 leading-relaxed break-words transition-all duration-200"
                style={{
                  display: "-webkit-box",
                  WebkitLineClamp: expandedTitle ? "unset" : 2,
                  WebkitBoxOrient: "vertical",
                  overflow: expandedTitle ? "visible" : "hidden",
                  textOverflow: expandedTitle ? "unset" : "ellipsis"
                }}
              >
                {data.title || "Title goes here."}
              </h4>
            </div>

            {/* Description */}
            <div className="mb-4">
              <p className="text-gray-500 text-xs leading-relaxed whitespace-pre-wrap break-words font-bold">
                {data.description || "Description goes here."}
              </p>
            </div>

            {/* Attachments */}
            <div className="mb-4 px-2">
              {data.attachments && data.attachments.length > 0 ? (
                <div className="space-y-3">
                  {/* Main/First Image/PDF - Display prominently */}
                  <div
                    className={`relative bg-gray-100 overflow-hidden transition-all duration-200 w-full max-w-[316px] h-[243px] rounded-[8px] ${
                      isInModal
                        ? "border-4 border-primary shadow-xl"
                        : "border-2 border-gray-200"
                    }`}
                  >
                    {isPDF(data.attachments[0].name, data.attachments[0].type) ? (
                      <div className="w-full h-full flex items-center justify-center">
                        <VscFilePdf className="w-16 h-16 text-red-600 font-bold" />
                        <div className="ml-3 text-center">
                          <div className="text-sm font-medium text-gray-900">
                            PDF Document
                          </div>
                          <div className="text-xs text-gray-500">
                            {data.attachments[0].name}
                          </div>
                        </div>
                      </div>
                    ) : isDoc(data.attachments[0].name, data.attachments[0].type) ? (
                      <div className="w-full h-full flex items-center justify-center">
                        <FaFileWord className="w-16 h-16 text-blue-600" />
                        <div className="ml-3 text-center">
                          <div className="text-sm font-medium text-gray-900">
                            Word Document
                          </div>
                          <div className="text-xs text-gray-500">
                            {data.attachments[0].name}
                          </div>
                        </div>
                      </div>
                    ) : (
                      <>
                        <img
                          src={data.attachments[currentImageIndex]?.file_url || data.attachments[currentImageIndex]?.url}
                          alt={`Attachment ${currentImageIndex + 1}`}
                          className="w-full h-full object-cover"
                        />
                        {data.attachments.length > 1 && (
                          <>
                            <button
                              onClick={prevImage}
                              className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-opacity"
                            >
                              ←
                            </button>
                            <button
                              onClick={nextImage}
                              className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-opacity"
                            >
                              →
                            </button>

                            {/* Image Counter */}
                            <div className="absolute bottom-2 right-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-sm">
                              {currentImageIndex + 1} / {data.attachments.length}
                            </div>
                          </>
                        )}
                      </>
                    )}
                  </div>

                  {/* Additional attachments indicator */}
                  {data.attachments.length > 1 && (
                    <div className="text-xs text-gray-500 text-center">
                      +{data.attachments.length - 1} more attachment{data.attachments.length > 2 ? "s" : ""}
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-xs text-gray-500 text-center py-4">
                  No attachments
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NoticePreview;
